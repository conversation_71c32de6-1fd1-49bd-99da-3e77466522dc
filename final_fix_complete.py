#!/usr/bin/env python3
"""
Final fix for complete Shopify import with correct column mapping
"""

import csv

def fix_complete_shopify_csv():
    """Fix the column mapping in the complete CSV"""
    
    print("Fixing column mapping in complete CSV...")
    
    fixed_rows = []
    
    with open('shopify-import-COMPLETE.csv', 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        fieldnames = reader.fieldnames
        
        for row in reader:
            # Fix the Option1 Value - it should not contain image URLs
            option_value = row.get('Option1 Value', '').strip()
            
            # If Option1 Value contains http (image URL), remove it
            if option_value and option_value.startswith('http'):
                # Extract letter from title if possible
                title = row.get('Title', '')
                if 'حروف بني' in title and ' - ' in title:
                    # Try to extract from title, but for now just set to empty
                    row['Option1 Value'] = ''
                else:
                    row['Option1 Value'] = ''
            
            # If no variant info, remove Option1 Name too
            if not row.get('Option1 Value'):
                row['Option1 Name'] = ''
            
            fixed_rows.append(row)
    
    # Write fixed CSV
    with open('shopify-import-ALL-PRODUCTS.csv', 'w', encoding='utf-8', newline='') as f:
        writer = csv.DictWriter(f, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(fixed_rows)
    
    print(f"✅ Fixed complete CSV: {len(fixed_rows)} products")
    print(f"📁 File: shopify-import-ALL-PRODUCTS.csv")
    
    # Count products by type
    letters_count = sum(1 for row in fixed_rows if 'حروف' in row.get('Title', ''))
    bags_count = sum(1 for row in fixed_rows if 'حقيبة' in row.get('Title', ''))
    
    print(f"📊 Letters products: {letters_count}")
    print(f"📊 Bags products: {bags_count}")
    print(f"📊 Total: {len(fixed_rows)}")

if __name__ == "__main__":
    fix_complete_shopify_csv()
