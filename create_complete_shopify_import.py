#!/usr/bin/env python3
"""
Create complete Shopify import with ALL products from Salla
"""

import csv
import hashlib
from collections import defaultdict

def create_handle(title, product_id):
    """Create unique handle"""
    title_lower = title.lower()
    
    if 'حروف بني' in title:
        base = 'brown-letters'
    elif 'حروف عنابي' in title or 'حروف -عنابي' in title:
        base = 'burgundy-letters'
    elif 'حروف أخضر' in title or 'حروف- أخضر' in title:
        base = 'green-letters'
    elif 'حروف' in title:
        base = 'letters'
    elif 'سيفين ونخله' in title:
        base = 'swords-palm'
    elif 'حقيبة سهرة' in title:
        base = 'evening-bag'
    elif 'حقيبة كتف' in title:
        base = 'shoulder-bag'
    elif 'حقيبة يد' in title:
        base = 'handbag'
    elif 'حقيبة كروس' in title or 'كروس بودي' in title:
        base = 'crossbody-bag'
    elif 'حقيبة باوتش' in title:
        base = 'pouch-bag'
    elif 'حقيبة توتي' in title:
        base = 'tote-bag'
    elif 'حقيبة ظهر' in title:
        base = 'backpack'
    elif 'حقيبة' in title:
        base = 'bag'
    else:
        # Create from title hash
        title_hash = hashlib.md5(title.encode('utf-8')).hexdigest()[:8]
        base = f'product-{title_hash}'
    
    return f"{base}-{product_id}"

def create_complete_shopify_import():
    """Create complete Shopify import with all products"""
    
    print("Creating complete Shopify import...")
    
    # Shopify headers
    headers = [
        'Handle', 'Title', 'Body (HTML)', 'Vendor', 'Product Type', 'Tags',
        'Published', 'Option1 Name', 'Option1 Value', 'Option2 Name', 'Option2 Value',
        'Option3 Name', 'Option3 Value', 'Variant SKU', 'Variant Grams',
        'Variant Inventory Tracker', 'Variant Inventory Qty', 'Variant Inventory Policy',
        'Variant Fulfillment Service', 'Variant Price', 'Variant Compare At Price',
        'Variant Requires Shipping', 'Variant Taxable', 'Variant Barcode',
        'Image Src', 'Image Position', 'Image Alt Text', 'Gift Card',
        'SEO Title', 'SEO Description', 'Google Shopping / Google Product Category',
        'Google Shopping / MPN', 'Google Shopping / Age Group', 'Google Shopping / Gender',
        'Google Shopping / Custom Product', 'Google Shopping / Custom Label 0',
        'Google Shopping / Custom Label 1', 'Google Shopping / Custom Label 2',
        'Google Shopping / Custom Label 3', 'Google Shopping / Custom Label 4',
        'Variant Image', 'Variant Weight Unit', 'Status'
    ]
    
    # Read original Salla data - treat each row as separate product
    rows = []
    products_processed = 0

    with open('chrisbella_24-08-2025-12-00_jCmuZwaX8DClwrXDKhYVyL1rSD1f62pl9wUURec4_products.csv', 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)

        for row in reader:
            product_name = row.get('سعر التخفيض', '').strip()  # Product name is in this column

            # Skip if not a valid product name
            if not product_name or product_name.replace('.', '').isdigit():
                continue

            # Skip main product rows (منتج) - we only want individual variants/options
            mpn = row.get('MPN', '').strip()
            if mpn == 'منتج':
                continue

            # Create handle and basic info
            product_id = row.get('GTIN', '')[:8] or str(hash(product_name + str(products_processed)))[:8]

            # Get variant info
            variant_name = row.get('[2] القيمة', '').strip()
            variant_sku = row.get('رمز المنتج sku', '').strip()
            variant_image = row.get('[2] الصورة / اللون', '').strip()

            # Create unique handle for each variant
            if variant_name:
                handle = create_handle(f"{product_name}-{variant_name}", product_id)
            else:
                handle = create_handle(product_name, product_id)

            # Determine price
            price = '39.00' if 'حروف' in product_name else '219.00'

            # Get product type
            if 'حروف' in product_name:
                product_type = 'حروف'
                tags = f'{product_name}, حروف, عربي'
                option_name = 'Letter'
            else:
                product_type = 'حقائب'
                tags = f'{product_name}, حقائب'
                option_name = 'Color'

            # Create product title with variant
            if variant_name:
                full_title = f"{product_name} - {variant_name}"
            else:
                full_title = product_name

            # Create product row
            product_row = {
                'Handle': handle,
                'Title': full_title,
                'Body (HTML)': f'<p>{product_name}</p>',
                'Vendor': 'ChrisBella',
                'Product Type': product_type,
                'Tags': tags,
                'Published': 'TRUE',
                'Option1 Name': option_name if variant_name else '',
                'Option1 Value': variant_name if variant_name else '',
                'Variant SKU': variant_sku if variant_sku else product_id,
                'Variant Grams': '100' if 'حروف' in product_name else '500',
                'Variant Inventory Tracker': 'shopify',
                'Variant Inventory Qty': '10',
                'Variant Inventory Policy': 'deny',
                'Variant Fulfillment Service': 'manual',
                'Variant Price': price,
                'Variant Requires Shipping': 'TRUE',
                'Variant Taxable': 'TRUE',
                'Image Src': variant_image if variant_image and variant_image.startswith('http') else '',
                'Image Position': '1',
                'Image Alt Text': full_title,
                'Gift Card': 'FALSE',
                'SEO Title': full_title,
                'SEO Description': product_name,
                'Google Shopping / Age Group': 'adult',
                'Google Shopping / Gender': 'unisex',
                'Google Shopping / Custom Product': 'FALSE',
                'Variant Image': variant_image if variant_image and variant_image.startswith('http') else '',
                'Variant Weight Unit': 'g',
                'Status': 'active'
            }
            rows.append(product_row)

            products_processed += 1
            if products_processed % 100 == 0:
                print(f"Processed {products_processed} products...")
    
    # Write complete CSV
    with open('shopify-import-COMPLETE.csv', 'w', encoding='utf-8', newline='') as f:
        writer = csv.DictWriter(f, fieldnames=headers)
        writer.writeheader()
        writer.writerows(rows)
    
    print(f"\n✅ Complete Shopify import created!")
    print(f"📊 Products: {products_processed}")
    print(f"📊 Total rows: {len(rows)}")
    print(f"📁 File: shopify-import-COMPLETE.csv")
    
    # Count variants and images
    variants = sum(1 for row in rows if row.get('Variant SKU'))
    images = sum(1 for row in rows if row.get('Image Src') and not row.get('Variant SKU'))
    
    print(f"📊 Expected import: {products_processed} products, {variants} variants, {images} additional images")

if __name__ == "__main__":
    create_complete_shopify_import()
