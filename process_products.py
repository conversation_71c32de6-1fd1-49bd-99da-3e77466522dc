#!/usr/bin/env python3
import csv
import re
from collections import defaultdict

def clean_text(text):
    """Clean and escape text for CSV"""
    if not text:
        return ""
    # Remove extra quotes and escape existing ones
    text = str(text).replace('""', '"').strip()
    return text

def extract_base_sku(sku):
    """Extract base SKU by removing color suffix"""
    if not sku:
        return ""
    # Remove common color suffixes
    sku = str(sku)
    color_suffixes = ['black', 'white', 'red', 'blue', 'green', 'pink', 'gold', 'silver', 
                     'brown', 'navy', 'beige', 'cream', 'champagne', 'gun', 'siver', 'silvar']
    
    for suffix in color_suffixes:
        if sku.lower().endswith(suffix.lower()):
            return sku[:-len(suffix)]
    
    # If no color suffix found, return as is
    return sku

def process_csv():
    products = defaultdict(list)
    
    # Read the original CSV
    with open('cb-sfy.csv', 'r', encoding='utf-8') as file:
        reader = csv.DictReader(file)
        
        for row in reader:
            mpn = row.get('MPN', '').strip()
            if mpn in ['منتج', 'خيار'] and row.get('Variant SKU'):
                base_sku = extract_base_sku(row['Variant SKU'])
                if base_sku:
                    products[base_sku].append(row)
    
    # Write the processed CSV
    with open('ready_products_with_variants_and_skus.csv', 'w', encoding='utf-8', newline='') as file:
        fieldnames = [
            'Handle', 'Title', 'Body (HTML)', 'Vendor', 'Product Category', 'Type', 'Tags', 'Published',
            'Option1 Name', 'Option1 Value', 'Option2 Name', 'Option2 Value', 'Option3 Name', 'Option3 Value',
            'Variant SKU', 'Variant Grams', 'Variant Inventory Tracker', 'Variant Inventory Qty',
            'Variant Inventory Policy', 'Variant Fulfillment Service', 'Variant Price', 'Variant Compare At Price',
            'Variant Requires Shipping', 'Variant Taxable', 'Variant Barcode', 'Image Src', 'Image Position',
            'Image Alt Text', 'Gift Card', 'SEO Title', 'SEO Description', 'Google Shopping / Google Product Category',
            'Google Shopping / Gender', 'Google Shopping / Age Group', 'Google Shopping / MPN',
            'Google Shopping / AdWords Grouping', 'Google Shopping / AdWords Labels', 'Google Shopping / Condition',
            'Google Shopping / Custom Product', 'Google Shopping / Custom Label 0', 'Google Shopping / Custom Label 1',
            'Google Shopping / Custom Label 2', 'Google Shopping / Custom Label 3', 'Google Shopping / Custom Label 4',
            'Variant Image', 'Variant Weight Unit', 'Status'
        ]
        
        writer = csv.DictWriter(file, fieldnames=fieldnames)
        writer.writeheader()
        
        for base_sku, variants in products.items():
            # Find the main product (منتج) for this SKU group
            main_product = None
            variant_products = []
            
            for variant in variants:
                if variant.get('MPN') == 'منتج':
                    main_product = variant
                else:
                    variant_products.append(variant)
            
            if not main_product and variant_products:
                # If no main product found, use first variant as template
                main_product = variant_products[0]
                variant_products = variant_products[1:]
            
            if main_product:
                # Process all variants for this product
                all_variants = [main_product] + variant_products
                
                for i, variant in enumerate(all_variants):
                    # Extract color from Option1 Value or SKU
                    color = variant.get('Option1 Value', '').strip()
                    if not color and variant.get('Variant SKU'):
                        # Try to extract color from SKU
                        sku = variant['Variant SKU']
                        for color_name in ['black', 'white', 'red', 'blue', 'green', 'pink', 'gold', 'silver', 
                                         'brown', 'navy', 'beige', 'cream', 'champagne', 'gun']:
                            if color_name in sku.lower():
                                color = color_name.title()
                                break
                    
                    # Get main product images
                    main_images = main_product.get('Image Src', '').split(',') if main_product.get('Image Src') else []
                    main_image = main_images[0].strip() if main_images else ''
                    
                    # Get variant image
                    variant_image = variant.get('Variant Image', '').strip()
                    
                    row_data = {
                        'Handle': base_sku,
                        'Title': clean_text(main_product.get('Title', '')),
                        'Body (HTML)': clean_text(main_product.get('Body (HTML)', '')),
                        'Vendor': 'ChrisBella',
                        'Product Category': clean_text(main_product.get('Product Category', '')),
                        'Type': 'Handbags',
                        'Tags': f"حقائب, نسائية, إكسسوارات, {clean_text(main_product.get('Title', ''))}",
                        'Published': 'TRUE',
                        'Option1 Name': 'اللون',
                        'Option1 Value': color,
                        'Option2 Name': '',
                        'Option2 Value': '',
                        'Option3 Name': '',
                        'Option3 Value': '',
                        'Variant SKU': clean_text(variant.get('Variant SKU', '')),
                        'Variant Grams': '',
                        'Variant Inventory Tracker': '',
                        'Variant Inventory Qty': '1',
                        'Variant Inventory Policy': 'deny',
                        'Variant Fulfillment Service': 'manual',
                        'Variant Price': clean_text(variant.get('Variant Price', '219.00')),
                        'Variant Compare At Price': '',
                        'Variant Requires Shipping': 'TRUE',
                        'Variant Taxable': 'TRUE',
                        'Variant Barcode': clean_text(variant.get('GTIN', '')),
                        'Image Src': main_image,
                        'Image Position': str(i + 1),
                        'Image Alt Text': f"{clean_text(main_product.get('Title', ''))} - {color}",
                        'Gift Card': 'FALSE',
                        'SEO Title': f"{clean_text(main_product.get('Title', ''))} - ChrisBella",
                        'SEO Description': f"{clean_text(main_product.get('Title', ''))} من ChrisBella متوفرة بألوان متعددة",
                        'Google Shopping / Google Product Category': 'Apparel & Accessories > Handbags',
                        'Google Shopping / Gender': 'Female',
                        'Google Shopping / Age Group': 'Adult',
                        'Google Shopping / MPN': base_sku,
                        'Google Shopping / AdWords Grouping': 'Handbags',
                        'Google Shopping / AdWords Labels': 'Luxury Handbags',
                        'Google Shopping / Condition': 'New',
                        'Google Shopping / Custom Product': 'FALSE',
                        'Google Shopping / Custom Label 0': 'ChrisBella',
                        'Google Shopping / Custom Label 1': 'Handbags',
                        'Google Shopping / Custom Label 2': 'Luxury',
                        'Google Shopping / Custom Label 3': 'Accessories',
                        'Google Shopping / Custom Label 4': 'Women',
                        'Variant Image': variant_image,
                        'Variant Weight Unit': 'kg',
                        'Status': 'active'
                    }
                    
                    writer.writerow(row_data)

if __name__ == "__main__":
    process_csv()
    print("Processing complete! Check ready_products_with_variants_and_skus.csv")
